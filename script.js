document.addEventListener('DOMContentLoaded', () => {
  // Section management
  const sections = document.querySelectorAll('.section');
  const sectionsContainer = document.getElementById('sections-container');
  let currentSection = 0;
  
  // Navigation elements
  const nav = document.querySelector('.navbar');
  const navLinks = document.querySelector('.nav-links');
  const hamburger = document.querySelector('.hamburger');
  
  // Scroll tracking for navbar show/hide
  let lastScrollTop = 0;
  const scrollThreshold = 50;
  
  // Initialize sections
  sections[currentSection].classList.add('active');
  
  // Navbar scroll behavior
  window.addEventListener('scroll', () => {
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    
    if (Math.abs(scrollTop - lastScrollTop) > scrollThreshold) {
      if (scrollTop > lastScrollTop) {
        // Scrolling down
        nav.style.transform = 'translateY(-100%)';
      } else {
        // Scrolling up
        nav.style.transform = 'translateY(0)';
      }
      lastScrollTop = scrollTop;
    }
  });
  
  // Section navigation with scroll/touch
  const navigateSections = (direction) => {
    const nextSection = currentSection + direction;
    
    if (nextSection >= 0 && nextSection < sections.length) {
      sections[currentSection].classList.remove('active');
      sections[currentSection].classList.add('leaving');
      
      setTimeout(() => {
        sections[currentSection].classList.remove('leaving');
        currentSection = nextSection;
        sections[currentSection].classList.add('active');
      }, 300);
    }
  };
  
  // Mouse wheel navigation
  window.addEventListener('wheel', (e) => {
    if (Math.abs(e.deltaY) < 50) return;
    navigateSections(e.deltaY > 0 ? 1 : -1);
  });
  
  // Touch swipe navigation
  let touchStartY = 0;
  
  window.addEventListener('touchstart', (e) => {
    touchStartY = e.touches[0].clientY;
  });
  
  window.addEventListener('touchend', (e) => {
    const touchEndY = e.changedTouches[0].clientY;
    const swipeDistance = touchStartY - touchEndY;
    
    if (Math.abs(swipeDistance) > 50) {
      navigateSections(swipeDistance > 0 ? -1 : 1);
    }
  });
  
  // Nav link clicks
  document.querySelectorAll('.nav-link').forEach(link => {
    link.addEventListener('click', (e) => {
      e.preventDefault();
      const targetIndex = parseInt(link.dataset.index);
      
      sections[currentSection].classList.remove('active');
      sections[targetIndex].classList.add('active');
      currentSection = targetIndex;
    });
  });
  
  // Mobile menu toggle
  hamburger.addEventListener('click', () => {
    navLinks.classList.toggle('active');
  });
  
  // Parallax effect for About section
  if (sections[0]) {
    window.addEventListener('scroll', () => {
      const scrollY = window.scrollY;
      sections[0].querySelector('.parallax-bg').style.transform = 
        `translateY(${scrollY * 0.5}px)`;
    });
  }
});
