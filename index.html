<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Portfolio</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="styles.css">
</head>
<body class="overflow-x-hidden">
    <!-- Navigation Bar -->
    <nav class="navbar">
        <div class="text-white font-bold text-xl">Portfolio</div>
        <ul class="nav-links">
            <li><a href="#about" class="nav-link" data-index="0">About</a></li>
            <li><a href="#skills" class="nav-link" data-index="1">Skills</a></li>
            <li><a href="#projects" class="nav-link" data-index="2">Projects</a></li>
            <li><a href="#contact" class="nav-link" data-index="3">Contact</a></li>
        </ul>
        <div class="hamburger">
            <span></span>
            <span></span>
            <span></span>
        </div>
    </nav>
    
    <main id="sections-container">
        <!-- About Section -->
        <section id="about" class="section" style="background: linear-gradient(135deg, #dbeafe, #bfdbfe);">
            <div class="parallax-bg" style="background-image: url('https://images.unsplash.com/photo-1503376780353-7e6692767b70?auto=format&fit=crop&q=80');"></div>
            <div class="max-w-4xl text-center z-10">
                <div class="bg-white bg-opacity-90 rounded-full w-48 h-48 mx-auto mb-8 overflow-hidden">
                    <div class="bg-gray-200 border-2 border-dashed rounded-xl w-full h-full flex items-center justify-center text-gray-400">
                        Profile Image
                    </div>
                </div>
                <h1 class="text-4xl md:text-5xl font-bold mb-4 text-slate-800">Your Name</h1>
                <p class="text-lg md:text-xl text-slate-700 max-w-2xl mx-auto">
                    Passionate developer specializing in creating modern web applications. 
                    With 5+ years of experience in full-stack development, I focus on building 
                    intuitive user experiences with clean, efficient code.
                </p>
            </div>
        </section>

        <!-- Skills Section -->
        <section id="skills" class="section" style="background: linear-gradient(135deg, #bfdbfe, #93c5fd);">
            <div class="max-w-4xl w-full">
                <h2 class="text-3xl md:text-4xl font-bold text-center mb-12 text-white">Skills & Expertise</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <div class="bg-white bg-opacity-90 p-6 rounded-xl">
                        <h3 class="text-xl font-bold mb-4 text-slate-800">Frontend</h3>
                        <ul class="space-y-2">
                            <li class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm inline-block mr-2">React</li>
                            <li class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm inline-block mr-2">Vue.js</li>
                            <li class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm inline-block mr-2">Tailwind CSS</li>
                            <li class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm inline-block mr-2">JavaScript</li>
                            <li class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm inline-block mr-2">TypeScript</li>
                        </ul>
                    </div>
                    <div class="bg-white bg-opacity-90 p-6 rounded-xl">
                        <h3 class="text-xl font-bold mb-4 text-slate-800">Backend</h3>
                        <ul class="space-y-2">
                            <li class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm inline-block mr-2">Node.js</li>
                            <li class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm inline-block mr-2">Express</li>
                            <li class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm inline-block mr-2">MongoDB</li>
                            <li class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm inline-block mr-2">Python</li>
                            <li class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm inline-block mr-2">Django</li>
                        </ul>
                    </div>
                    <div class="bg-white bg-opacity-90 p-6 rounded-xl">
                        <h3 class="text-xl font-bold mb-4 text-slate-800">Tools & Practices</h3>
                        <ul class="space-y-2">
                            <li class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm inline-block mr-2">Git & GitHub</li>
                            <li class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm inline-block mr-2">Docker</li>
                            <li class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm inline-block mr-2">CI/CD</li>
                            <li class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm inline-block mr-2">Agile</li>
                            <li class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm inline-block mr-2">Testing</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- Projects Section -->
        <section id="projects" class="section" style="background: linear-gradient(135deg, #93c5fd, #60a5fa);">
            <div class="max-w-6xl w-full px-4">
                <h2 class="text-3xl md:text-4xl font-bold text-center mb-12 text-white">Featured Projects</h2>
                <div class="projects-grid">
                    <!-- Project 1 -->
                    <div class="project-card">
                        <div class="bg-gray-200 border-2 border-dashed w-full h-48"></div>
                        <div class="p-6">
                            <h3 class="text-xl font-bold mb-2">E-commerce Platform</h3>
                            <p class="text-gray-600 mb-4">Full-featured online shopping experience with cart, payments, and admin dashboard.</p>
                            <div class="flex flex-wrap gap-2 mb-4">
                                <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-xs">React</span>
                                <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-xs">Node.js</span>
                                <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-xs">MongoDB</span>
                            </div>
                            <a href="#" class="text-blue-600 font-medium hover:underline">View Project →</a>
                        </div>
                    </div>
                    
                    <!-- Project 2 -->
                    <div class="project-card">
                        <div class="bg-gray-200 border-2 border-dashed w-full h-48"></div>
                        <div class="p-6">
                            <h3 class="text-xl font-bold mb-2">Task Management App</h3>
                            <p class="text-gray-600 mb-4">Kanban-style task manager with drag-and-drop interface and team collaboration.</p>
                            <div class="flex flex-wrap gap-2 mb-4">
                                <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-xs">Vue.js</span>
                                <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-xs">Firebase</span>
                                <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-xs">Tailwind</span>
                            </div>
                            <a href="#" class="text-blue-600 font-medium hover:underline">View Project →</a>
                        </div>
                    </div>
                    
                    <!-- Project 3 -->
                    <div class="project-card">
                        <div class="bg-gray-200 border-2 border-dashed w-full h-48"></div>
                        <div class="p-6">
                            <h3 class="text-xl font-bold mb-2">Health Tracking Dashboard</h3>
                            <p class="text-gray-600 mb-4">Data visualization platform for health metrics with personalized insights.</p>
                            <div class="flex flex-wrap gap-2 mb-4">
                                <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-xs">React</span>
                                <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-xs">D3.js</span>
                                <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-xs">Python</span>
                            </div>
                            <a href="#" class="text-blue-600 font-medium hover:underline">View Project →</a>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Contact Section -->
        <section id="contact" class="section" style="background: linear-gradient(135deg, #60a5fa, #3b82f6);">
            <div class="max-w-4xl w-full text-center">
                <h2 class="text-3xl md:text-4xl font-bold mb-12 text-white">Get In Touch</h2>
                <div class="bg-white bg-opacity-90 rounded-2xl p-8 max-w-2xl mx-auto">
                    <p class="text-lg text-gray-700 mb-8">
                        Have a project in mind or want to discuss opportunities? Feel free to reach out!
                    </p>
                    <div class="space-y-4 text-left max-w-md mx-auto">
                        <div class="flex items-center">
                            <svg class="w-6 h-6 text-blue-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                            <span><EMAIL></span>
                        </div>
                        <div class="flex items-center">
                            <svg class="w-6 h-6 text-blue-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            <span>San Francisco, CA</span>
                        </div>
                    </div>
                    
                    <div class="social-links mt-8">
                        <a href="#" class="bg-blue-500 text-white p-3 rounded-full hover:bg-blue-600 transition">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 0c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm-2 20h-2v-10h2v10zm-1-12.272c-.826 0-1.5-.673-1.5-1.5s.674-1.5 1.5-1.5 1.5.673 1.5 1.5-.674 1.5-1.5 1.5zm13 12.272h-2v-6c0-.771-.022-1.771-1-1.771-1 0-1.198.796-1.198 1.734v6.037h-2v-10h2v1.024h.03c.318-.6 1.092-1.233 2.246-1.233 2.4 0 2.845 1.58 2.845 3.637v6.572z"/>
                            </svg>
                        </a>
                        <a href="#" class="bg-blue-500 text-white p-3 rounded-full hover:bg-blue-600 transition">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                            </svg>
                        </a>
                        <a href="#" class="bg-blue-500 text-white p-3 rounded-full hover:bg-blue-600 transition">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                            </svg>
                        </a>
                    </div>
                </div>
                
                <!-- Footer -->
                <div class="footer mt-16">
                    <p>© 2025 Portfolio Website. All rights reserved.</p>
                </div>
            </div>
        </section>
    </main>
    <script src="script.js"></script>
</body>
</html>
